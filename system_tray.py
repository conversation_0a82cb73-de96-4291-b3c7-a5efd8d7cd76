"""
Manejo de la bandeja del sistema (system tray) para la aplicación.
Proporciona un icono en la bandeja con menú contextual.
"""

import sys
import threading
from PIL import Image, ImageDraw
import pystray
from pystray import MenuItem as item
import io

class SystemTrayManager:
    def __init__(self, app_controller):
        self.app_controller = app_controller
        self.icon = None
        self.is_running = False
        
    def create_icon_image(self) -> Image.Image:
        """Crea un icono simple para la bandeja del sistema."""
        # Crear imagen de 64x64 píxeles
        width = height = 64
        image = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        # Dibujar un círculo con gradiente
        center = width // 2
        radius = 24
        
        # Círculo exterior
        draw.ellipse([center-radius, center-radius, center+radius, center+radius], 
                    fill=(100, 150, 255, 255), outline=(50, 100, 200, 255), width=2)
        
        # Círculo interior
        inner_radius = radius - 8
        draw.ellipse([center-inner_radius, center-inner_radius, 
                     center+inner_radius, center+inner_radius], 
                    fill=(150, 200, 255, 200))
        
        # Punto central
        dot_radius = 4
        draw.ellipse([center-dot_radius, center-dot_radius, 
                     center+dot_radius, center+dot_radius], 
                    fill=(255, 255, 255, 255))
        
        return image
    
    def create_menu(self):
        """Crea el menú contextual para la bandeja del sistema."""
        return pystray.Menu(
            item('Abrir Panel de Control', self.show_control_panel),
            item('Alternar Filtros', self.toggle_filters),
            pystray.Menu.SEPARATOR,
            item('Perfiles', pystray.Menu(
                item('Guardar Perfil Actual', self.save_current_profile),
                item('Modo Noche', lambda: self.load_preset('night_mode')),
                item('Modo Lectura', lambda: self.load_preset('reading_mode')),
                item('Modo Cálido', lambda: self.load_preset('warm_mode')),
                item('Restablecer', self.reset_filters)
            )),
            pystray.Menu.SEPARATOR,
            item('Acerca de', self.show_about),
            item('Salir', self.quit_application)
        )
    
    def show_control_panel(self, icon=None, item=None):
        """Muestra el panel de control."""
        print("System tray: Solicitando mostrar panel de control")
        if self.app_controller:
            self.app_controller.show_control_panel()
        else:
            print("Error: No hay controlador de aplicación disponible")
    
    def toggle_filters(self, icon=None, item=None):
        """Alterna el estado de los filtros."""
        if self.app_controller:
            self.app_controller.toggle_overlay()
    
    def save_current_profile(self, icon=None, item=None):
        """Guarda el perfil actual con un nombre automático."""
        if self.app_controller:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            profile_name = f"Perfil_{timestamp}"
            self.app_controller.save_profile(profile_name)
    
    def load_preset(self, preset_name: str):
        """Carga un preset predefinido."""
        presets = {
            'night_mode': {
                'brightness': -30,
                'temperature': 40,
                'opacity': 80,
                'contrast': -10
            },
            'reading_mode': {
                'brightness': 10,
                'temperature': 20,
                'contrast': 15,
                'sepia': 20
            },
            'warm_mode': {
                'temperature': 60,
                'brightness': -10,
                'saturation': -20
            }
        }
        
        if preset_name in presets and self.app_controller:
            self.app_controller.apply_preset(presets[preset_name])
    
    def reset_filters(self, icon=None, item=None):
        """Restablece todos los filtros."""
        if self.app_controller:
            self.app_controller.reset_settings()
    
    def show_about(self, icon=None, item=None):
        """Muestra información sobre la aplicación."""
        # Nota: pystray no tiene diálogos nativos, así que usaremos print
        # En una implementación completa, podrías mostrar una ventana PyQt
        print("Visual Filter v1.0")
        print("Aplicación de filtros visuales globales para Windows")
        print("Desarrollado con Python, PyQt5 y pystray")
    
    def quit_application(self, icon=None, item=None):
        """Cierra la aplicación completamente."""
        if self.app_controller:
            self.app_controller.quit_application()
        self.stop()
    
    def start(self):
        """Inicia el icono de la bandeja del sistema."""
        if self.is_running:
            return
        
        try:
            # Crear icono
            icon_image = self.create_icon_image()
            menu = self.create_menu()
            
            self.icon = pystray.Icon(
                "visual_filter",
                icon_image,
                "Visual Filter",
                menu
            )
            
            self.is_running = True
            
            # Ejecutar en un hilo separado para no bloquear la aplicación principal
            def run_icon():
                try:
                    self.icon.run()
                except Exception as e:
                    print(f"Error en system tray: {e}")
                finally:
                    self.is_running = False
            
            self.tray_thread = threading.Thread(target=run_icon, daemon=True)
            self.tray_thread.start()
            
        except Exception as e:
            print(f"Error iniciando system tray: {e}")
            self.is_running = False
    
    def stop(self):
        """Detiene el icono de la bandeja del sistema."""
        if self.icon and self.is_running:
            try:
                self.icon.stop()
            except Exception as e:
                print(f"Error deteniendo system tray: {e}")
            finally:
                self.is_running = False
    
    def update_icon(self, enabled: bool = True):
        """Actualiza el icono para reflejar el estado actual."""
        if not self.icon:
            return
        
        try:
            # Crear nuevo icono basado en el estado
            icon_image = self.create_icon_image()
            
            if not enabled:
                # Hacer el icono más opaco si está deshabilitado
                icon_array = list(icon_image.getdata())
                faded_array = []
                for pixel in icon_array:
                    if len(pixel) == 4:  # RGBA
                        r, g, b, a = pixel
                        # Reducir opacidad
                        faded_array.append((r, g, b, a // 2))
                    else:  # RGB
                        faded_array.append(pixel)
                
                icon_image.putdata(faded_array)
            
            # Actualizar icono
            self.icon.icon = icon_image
            
        except Exception as e:
            print(f"Error actualizando icono: {e}")
    
    def show_notification(self, title: str, message: str):
        """Muestra una notificación del sistema."""
        if self.icon and self.is_running:
            try:
                self.icon.notify(message, title)
            except Exception as e:
                print(f"Error mostrando notificación: {e}")
    
    def is_tray_available(self) -> bool:
        """Verifica si la bandeja del sistema está disponible."""
        try:
            return pystray.Icon.HAS_NOTIFICATION
        except:
            return False
