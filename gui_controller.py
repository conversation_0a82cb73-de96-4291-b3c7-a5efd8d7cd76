"""
Panel de control con interfaz gráfica para ajustar los filtros visuales.
Contiene sliders y controles para todos los efectos disponibles.
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QSlider, QLabel, QPushButton, QGroupBox,
                            QComboBox, QLineEdit, QMessageBox, QCheckBox, QGridLayout)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

class FilterControlPanel(QMainWindow):
    # Señales para comunicar cambios
    settings_changed = pyqtSignal(dict)
    toggle_overlay = pyqtSignal()
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.sliders = {}
        self.labels = {}
        self.updating_ui = False
        
        self.init_ui()
        self.load_current_settings()

    def force_show(self):
        """Fuerza que la ventana aparezca en primer plano."""
        self.show()
        self.raise_()
        self.activateWindow()
        self.setWindowState(self.windowState() & ~Qt.WindowMinimized | Qt.WindowActive)

        # En Windows, forzar que aparezca
        try:
            import ctypes
            from ctypes import wintypes
            hwnd = int(self.winId())
            ctypes.windll.user32.SetForegroundWindow(hwnd)
            ctypes.windll.user32.BringWindowToTop(hwnd)
        except:
            pass  # Si falla, no importa

    def init_ui(self):
        """Inicializa la interfaz de usuario."""
        self.setWindowTitle("🎨 Visual Filter Control Panel")

        # Posicionar en el centro de la pantalla
        from PyQt5.QtWidgets import QDesktopWidget
        screen = QDesktopWidget().screenGeometry()
        x = (screen.width() - 400) // 2
        y = (screen.height() - 700) // 2
        self.setGeometry(x, y, 400, 700)

        # Configurar para que aparezca en primer plano
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_ShowWithoutActivating, False)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        
        # Título
        title_label = QLabel("🎨 Visual Filter Control")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        main_layout.addWidget(title_label)
        
        # Checkbox para habilitar/deshabilitar
        self.enable_checkbox = QCheckBox("Habilitar filtros")
        self.enable_checkbox.setChecked(self.config_manager.get_setting('enabled'))
        self.enable_checkbox.stateChanged.connect(self.on_enable_changed)
        main_layout.addWidget(self.enable_checkbox)
        
        # Crear grupos de controles
        main_layout.addWidget(self.create_basic_controls())
        main_layout.addWidget(self.create_color_controls())
        main_layout.addWidget(self.create_effect_controls())
        main_layout.addWidget(self.create_profile_controls())
        main_layout.addWidget(self.create_action_buttons())
        
        # Espaciador
        main_layout.addStretch()
    
    def create_basic_controls(self) -> QGroupBox:
        """Crea controles básicos (brillo, contraste, opacidad)."""
        group = QGroupBox("Controles Básicos")
        layout = QVBoxLayout(group)
        
        # Brillo
        self.add_slider_control(layout, "brightness", "🔆 Brillo", -100, 100, 0)
        
        # Contraste
        self.add_slider_control(layout, "contrast", "🔳 Contraste", -100, 100, 0)
        
        # Opacidad
        self.add_slider_control(layout, "opacity", "👁️ Opacidad", 0, 100, 100)
        
        return group
    
    def create_color_controls(self) -> QGroupBox:
        """Crea controles de color."""
        group = QGroupBox("Controles de Color")
        layout = QVBoxLayout(group)
        
        # Tono
        self.add_slider_control(layout, "hue", "🌈 Tono", -180, 180, 0)
        
        # Saturación
        self.add_slider_control(layout, "saturation", "🎨 Saturación", -100, 100, 0)
        
        # Temperatura
        self.add_slider_control(layout, "temperature", "🌡️ Temperatura", -100, 100, 0)
        
        return group
    
    def create_effect_controls(self) -> QGroupBox:
        """Crea controles de efectos especiales."""
        group = QGroupBox("Efectos Especiales")
        layout = QVBoxLayout(group)
        
        # Escala de grises
        self.add_slider_control(layout, "grayscale", "⚫ Escala de Grises", 0, 100, 0)
        
        # Sepia
        self.add_slider_control(layout, "sepia", "🟤 Sepia", 0, 100, 0)
        
        # Invertido
        self.add_slider_control(layout, "invert", "🔄 Invertido", 0, 100, 0)
        
        # Viñeta
        self.add_slider_control(layout, "vignette", "⭕ Viñeta", 0, 100, 0)
        
        # Desenfoque
        self.add_slider_control(layout, "blur", "💫 Desenfoque", 0, 10, 0)
        
        return group
    
    def create_profile_controls(self) -> QGroupBox:
        """Crea controles para perfiles."""
        group = QGroupBox("Perfiles")
        layout = QVBoxLayout(group)
        
        # Selector de perfiles
        profile_layout = QHBoxLayout()
        self.profile_combo = QComboBox()
        self.update_profile_list()
        profile_layout.addWidget(QLabel("Perfil:"))
        profile_layout.addWidget(self.profile_combo)
        layout.addLayout(profile_layout)
        
        # Entrada para nuevo perfil
        new_profile_layout = QHBoxLayout()
        self.new_profile_input = QLineEdit()
        self.new_profile_input.setPlaceholderText("Nombre del nuevo perfil...")
        new_profile_layout.addWidget(self.new_profile_input)
        layout.addLayout(new_profile_layout)
        
        # Botones de perfil
        profile_buttons_layout = QHBoxLayout()
        
        load_btn = QPushButton("Cargar")
        load_btn.clicked.connect(self.load_profile)
        profile_buttons_layout.addWidget(load_btn)
        
        save_btn = QPushButton("Guardar")
        save_btn.clicked.connect(self.save_profile)
        profile_buttons_layout.addWidget(save_btn)
        
        delete_btn = QPushButton("Eliminar")
        delete_btn.clicked.connect(self.delete_profile)
        profile_buttons_layout.addWidget(delete_btn)
        
        layout.addLayout(profile_buttons_layout)
        
        return group
    
    def create_action_buttons(self) -> QGroupBox:
        """Crea botones de acción."""
        group = QGroupBox("Acciones")
        layout = QHBoxLayout(group)
        
        # Botón reset
        reset_btn = QPushButton("🔄 Restablecer")
        reset_btn.clicked.connect(self.reset_settings)
        layout.addWidget(reset_btn)
        
        # Botón toggle overlay
        toggle_btn = QPushButton("👁️ Alternar Overlay")
        toggle_btn.clicked.connect(self.toggle_overlay.emit)
        layout.addWidget(toggle_btn)
        
        return group
    
    def add_slider_control(self, layout: QVBoxLayout, key: str, label: str, 
                          min_val: int, max_val: int, default_val: int):
        """Añade un control de slider con etiqueta."""
        # Layout horizontal para el control
        control_layout = QHBoxLayout()
        
        # Etiqueta
        label_widget = QLabel(label)
        label_widget.setMinimumWidth(120)
        control_layout.addWidget(label_widget)
        
        # Slider
        slider = QSlider(Qt.Horizontal)
        slider.setMinimum(min_val)
        slider.setMaximum(max_val)
        slider.setValue(default_val)
        slider.valueChanged.connect(lambda value, k=key: self.on_slider_changed(k, value))
        control_layout.addWidget(slider)
        
        # Etiqueta de valor
        value_label = QLabel(str(default_val))
        value_label.setMinimumWidth(40)
        value_label.setAlignment(Qt.AlignCenter)
        control_layout.addWidget(value_label)
        
        # Guardar referencias
        self.sliders[key] = slider
        self.labels[key] = value_label
        
        layout.addLayout(control_layout)
    
    def on_slider_changed(self, key: str, value: int):
        """Maneja cambios en los sliders."""
        if self.updating_ui:
            return
        
        # Actualizar etiqueta
        self.labels[key].setText(str(value))
        
        # Emitir señal de cambio
        self.settings_changed.emit({key: value})
    
    def on_enable_changed(self, state: int):
        """Maneja cambios en el checkbox de habilitación."""
        enabled = state == Qt.Checked
        self.settings_changed.emit({'enabled': enabled})
    
    def load_current_settings(self):
        """Carga la configuración actual en la interfaz."""
        self.updating_ui = True
        
        settings = self.config_manager.current_settings
        
        for key, slider in self.sliders.items():
            value = settings.get(key, 0)
            slider.setValue(value)
            self.labels[key].setText(str(value))
        
        self.enable_checkbox.setChecked(settings.get('enabled', True))
        
        self.updating_ui = False
    
    def reset_settings(self):
        """Restablece todos los valores a los predeterminados."""
        self.config_manager.reset_to_defaults()
        self.load_current_settings()
        
        # Emitir todos los cambios
        self.settings_changed.emit(self.config_manager.current_settings)
    
    def update_profile_list(self):
        """Actualiza la lista de perfiles en el combo box."""
        self.profile_combo.clear()
        profiles = self.config_manager.get_profiles()
        self.profile_combo.addItems(profiles)
    
    def save_profile(self):
        """Guarda el perfil actual."""
        name = self.new_profile_input.text().strip()
        if not name:
            QMessageBox.warning(self, "Error", "Por favor ingresa un nombre para el perfil.")
            return
        
        if self.config_manager.save_profile(name):
            QMessageBox.information(self, "Éxito", f"Perfil '{name}' guardado correctamente.")
            self.new_profile_input.clear()
            self.update_profile_list()
        else:
            QMessageBox.critical(self, "Error", "No se pudo guardar el perfil.")
    
    def load_profile(self):
        """Carga el perfil seleccionado."""
        profile_name = self.profile_combo.currentText()
        if not profile_name:
            return
        
        if self.config_manager.load_profile(profile_name):
            self.load_current_settings()
            self.settings_changed.emit(self.config_manager.current_settings)
            QMessageBox.information(self, "Éxito", f"Perfil '{profile_name}' cargado correctamente.")
        else:
            QMessageBox.critical(self, "Error", "No se pudo cargar el perfil.")
    
    def delete_profile(self):
        """Elimina el perfil seleccionado."""
        profile_name = self.profile_combo.currentText()
        if not profile_name:
            return
        
        reply = QMessageBox.question(self, "Confirmar", 
                                   f"¿Estás seguro de que quieres eliminar el perfil '{profile_name}'?",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            if self.config_manager.delete_profile(profile_name):
                self.update_profile_list()
                QMessageBox.information(self, "Éxito", f"Perfil '{profile_name}' eliminado correctamente.")
            else:
                QMessageBox.critical(self, "Error", "No se pudo eliminar el perfil.")
    
    def closeEvent(self, event):
        """Maneja el evento de cierre de la ventana."""
        self.hide()
        event.ignore()  # No cerrar realmente, solo ocultar
