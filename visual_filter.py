"""
Overlay visual principal que se superpone a toda la pantalla.
Maneja la captura de pantalla y aplicación de filtros en tiempo real.
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QLabel
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QPixmap, QPainter, QColor
from PIL import Image
import numpy as np
from filter_effects import FilterEffects

class VisualFilterOverlay(QWidget):
    # Señal para comunicar cambios de configuración
    settings_changed = pyqtSignal(dict)
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.is_active = True
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_overlay)
        
        self.init_ui()
        self.start_overlay()
    
    def init_ui(self):
        """Inicializa la interfaz del overlay."""
        # Configurar ventana para que cubra toda la pantalla
        self.setWindowFlags(
            Qt.WindowStaysOnTopHint |
            Qt.FramelessWindowHint |
            Qt.Tool |
            Qt.WindowTransparentForInput
        )
        
        # Hacer la ventana transparente
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_TransparentForMouseEvents)
        
        # Obtener dimensiones de la pantalla
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        self.setGeometry(screen_geometry)
        
        # Crear label para mostrar el overlay
        self.overlay_label = QLabel(self)
        self.overlay_label.setGeometry(self.rect())
        self.overlay_label.setScaledContents(True)
        
        # Mostrar la ventana
        self.show()
    
    def start_overlay(self):
        """Inicia el overlay visual."""
        if self.config_manager.get_setting('enabled'):
            self.update_timer.start(100)  # Actualizar cada 100ms
    
    def stop_overlay(self):
        """Detiene el overlay visual."""
        self.update_timer.stop()
        self.hide()
    
    def toggle_overlay(self):
        """Alterna el estado del overlay."""
        if self.is_active:
            self.stop_overlay()
            self.is_active = False
        else:
            self.start_overlay()
            self.is_active = True
    
    def update_overlay(self):
        """Actualiza el overlay con los filtros actuales."""
        try:
            if not self.config_manager.get_setting('enabled'):
                self.overlay_label.clear()
                return

            # Crear overlay directamente con Qt
            screen = QApplication.primaryScreen()
            screen_size = screen.size()

            # Crear overlay usando solo Qt
            self.create_qt_overlay(screen_size.width(), screen_size.height())

        except Exception as e:
            print(f"Error actualizando overlay: {e}")
            # En caso de error, limpiar el overlay
            self.overlay_label.clear()
    
    def create_qt_overlay(self, width: int, height: int):
        """Crea un overlay usando solo Qt."""
        try:
            from PyQt5.QtGui import QPainter, QColor

            # Crear pixmap transparente
            pixmap = QPixmap(width, height)
            pixmap.fill(QColor(0, 0, 0, 0))  # Completamente transparente

            painter = QPainter(pixmap)

            # Obtener configuraciones
            settings = self.config_manager.current_settings
            brightness = settings.get('brightness', 0)
            contrast = settings.get('contrast', 0)
            temperature = settings.get('temperature', 0)
            hue = settings.get('hue', 0)
            saturation = settings.get('saturation', 0)
            grayscale = settings.get('grayscale', 0)
            sepia = settings.get('sepia', 0)
            invert = settings.get('invert', 0)
            opacity = settings.get('opacity', 100)
            vignette = settings.get('vignette', 0)

            # Aplicar overlay de brillo
            if brightness != 0:
                alpha = min(255, int(abs(brightness) * 2))
                if brightness > 0:
                    # Brillo positivo - overlay blanco
                    color = QColor(255, 255, 255, alpha)
                else:
                    # Brillo negativo - overlay negro
                    color = QColor(0, 0, 0, alpha)
                painter.fillRect(0, 0, width, height, color)

            # Aplicar overlay de temperatura
            if temperature != 0:
                alpha = min(255, int(abs(temperature) * 1.5))
                if temperature > 0:  # Más cálido
                    color = QColor(255, 200, 150, alpha)
                else:  # Más frío
                    color = QColor(150, 200, 255, alpha)
                painter.fillRect(0, 0, width, height, color)

            # Aplicar overlay de tono
            if hue != 0:
                alpha = min(255, int(abs(hue) * 0.8))
                # Calcular color basado en el tono
                import colorsys
                h = (hue + 180) / 360.0  # Normalizar
                r, g, b = [int(c * 255) for c in colorsys.hsv_to_rgb(h, 0.5, 1.0)]
                color = QColor(r, g, b, alpha)
                painter.fillRect(0, 0, width, height, color)

            # Aplicar overlay de escala de grises
            if grayscale > 0:
                alpha = min(255, int(grayscale * 2))
                color = QColor(128, 128, 128, alpha)
                painter.fillRect(0, 0, width, height, color)

            # Aplicar overlay sepia
            if sepia > 0:
                alpha = min(255, int(sepia * 1.5))
                color = QColor(200, 180, 140, alpha)
                painter.fillRect(0, 0, width, height, color)

            # Aplicar overlay invertido
            if invert > 0:
                alpha = min(255, int(invert * 2))
                color = QColor(128, 128, 128, alpha)
                painter.fillRect(0, 0, width, height, color)

            # Aplicar viñeta
            if vignette > 0:
                self.apply_vignette_qt(painter, width, height, vignette)

            painter.end()

            # Aplicar opacidad global
            if opacity < 100:
                final_pixmap = QPixmap(width, height)
                final_pixmap.fill(QColor(0, 0, 0, 0))
                final_painter = QPainter(final_pixmap)
                final_painter.setOpacity(opacity / 100.0)
                final_painter.drawPixmap(0, 0, pixmap)
                final_painter.end()
                pixmap = final_pixmap

            self.overlay_label.setPixmap(pixmap)

        except Exception as e:
            print(f"Error creando overlay Qt: {e}")

    def apply_vignette_qt(self, painter, width: int, height: int, intensity: int):
        """Aplica efecto viñeta usando Qt."""
        try:
            from PyQt5.QtGui import QRadialGradient, QBrush
            from PyQt5.QtCore import QPointF

            # Crear gradiente radial para viñeta
            center = QPointF(width / 2, height / 2)
            radius = max(width, height) / 2

            gradient = QRadialGradient(center, radius)
            gradient.setColorAt(0.0, QColor(0, 0, 0, 0))  # Centro transparente
            gradient.setColorAt(1.0, QColor(0, 0, 0, int(intensity * 2.55)))  # Bordes oscuros

            brush = QBrush(gradient)
            painter.fillRect(0, 0, width, height, brush)

        except Exception as e:
            print(f"Error aplicando viñeta: {e}")




    def update_settings(self, new_settings: dict):
        """Actualiza la configuración y refresca el overlay."""
        for key, value in new_settings.items():
            self.config_manager.set_setting(key, value)
        
        # Forzar actualización inmediata
        self.update_overlay()
    
    def set_enabled(self, enabled: bool):
        """Habilita o deshabilita el overlay."""
        self.config_manager.set_setting('enabled', enabled)
        if enabled:
            self.start_overlay()
            self.show()
        else:
            self.stop_overlay()
            self.hide()
    
    def closeEvent(self, event):
        """Maneja el evento de cierre."""
        self.stop_overlay()
        event.accept()
