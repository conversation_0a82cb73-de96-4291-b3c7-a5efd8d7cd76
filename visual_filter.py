"""
Overlay visual principal que se superpone a toda la pantalla.
Maneja la captura de pantalla y aplicación de filtros en tiempo real.
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QLabel
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QPixmap, QPainter, QColor
from PIL import Image, ImageQt
import numpy as np
from filter_effects import FilterEffects

class VisualFilterOverlay(QWidget):
    # Señal para comunicar cambios de configuración
    settings_changed = pyqtSignal(dict)
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.is_active = True
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_overlay)
        
        self.init_ui()
        self.start_overlay()
    
    def init_ui(self):
        """Inicializa la interfaz del overlay."""
        # Configurar ventana para que cubra toda la pantalla
        self.setWindowFlags(
            Qt.WindowStaysOnTopHint |
            Qt.FramelessWindowHint |
            Qt.Tool |
            Qt.WindowTransparentForInput
        )
        
        # Hacer la ventana transparente
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_TransparentForMouseEvents)
        
        # Obtener dimensiones de la pantalla
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        self.setGeometry(screen_geometry)
        
        # Crear label para mostrar el overlay
        self.overlay_label = QLabel(self)
        self.overlay_label.setGeometry(self.rect())
        self.overlay_label.setScaledContents(True)
        
        # Mostrar la ventana
        self.show()
    
    def start_overlay(self):
        """Inicia el overlay visual."""
        if self.config_manager.get_setting('enabled'):
            self.update_timer.start(100)  # Actualizar cada 100ms
    
    def stop_overlay(self):
        """Detiene el overlay visual."""
        self.update_timer.stop()
        self.hide()
    
    def toggle_overlay(self):
        """Alterna el estado del overlay."""
        if self.is_active:
            self.stop_overlay()
            self.is_active = False
        else:
            self.start_overlay()
            self.is_active = True
    
    def update_overlay(self):
        """Actualiza el overlay con los filtros actuales."""
        try:
            if not self.config_manager.get_setting('enabled'):
                self.overlay_label.clear()
                return

            # Crear imagen base para el overlay
            screen = QApplication.primaryScreen()
            screen_size = screen.size()

            # Crear imagen de color sólido como base
            overlay_image = Image.new('RGBA', (screen_size.width(), screen_size.height()), (0, 0, 0, 0))

            # Aplicar efectos de color como overlays
            settings = self.config_manager.current_settings
            overlay_image = self.create_color_overlay(overlay_image, settings)

            # Verificar que la imagen tiene contenido antes de mostrar
            if overlay_image:
                # Convertir a QPixmap y mostrar
                qt_image = ImageQt.ImageQt(overlay_image)
                pixmap = QPixmap.fromImage(qt_image)
                self.overlay_label.setPixmap(pixmap)

        except Exception as e:
            print(f"Error actualizando overlay: {e}")
            # En caso de error, limpiar el overlay
            self.overlay_label.clear()
    
    def create_color_overlay(self, base_image: Image.Image, settings: dict) -> Image.Image:
        """Crea un overlay de color basado en la configuración."""
        width, height = base_image.size
        overlay = Image.new('RGBA', (width, height), (0, 0, 0, 0))

        # Obtener configuraciones
        brightness = settings.get('brightness', 0)
        contrast = settings.get('contrast', 0)
        temperature = settings.get('temperature', 0)
        hue = settings.get('hue', 0)
        saturation = settings.get('saturation', 0)
        grayscale = settings.get('grayscale', 0)
        sepia = settings.get('sepia', 0)
        invert = settings.get('invert', 0)
        opacity = settings.get('opacity', 100)
        vignette = settings.get('vignette', 0)

        # Crear overlay de brillo
        if brightness != 0:
            alpha = int(abs(brightness) * 1.5)  # Intensidad del overlay
            if brightness > 0:
                # Brillo positivo - overlay blanco
                bright_overlay = Image.new('RGBA', (width, height), (255, 255, 255, alpha))
            else:
                # Brillo negativo - overlay negro
                bright_overlay = Image.new('RGBA', (width, height), (0, 0, 0, alpha))
            overlay = Image.alpha_composite(overlay, bright_overlay)

        # Crear overlay de temperatura de color
        if temperature != 0:
            alpha = int(abs(temperature) * 1.2)
            if temperature > 0:  # Más cálido
                temp_overlay = Image.new('RGBA', (width, height), (255, 200, 150, alpha))
            else:  # Más frío
                temp_overlay = Image.new('RGBA', (width, height), (150, 200, 255, alpha))
            overlay = Image.alpha_composite(overlay, temp_overlay)

        # Crear overlay de tono
        if hue != 0:
            alpha = int(abs(hue) * 0.8)
            # Calcular color basado en el tono
            import colorsys
            h = (hue + 180) / 360.0  # Normalizar y centrar
            r, g, b = [int(c * 255) for c in colorsys.hsv_to_rgb(h, 0.5, 1.0)]
            hue_overlay = Image.new('RGBA', (width, height), (r, g, b, alpha))
            overlay = Image.alpha_composite(overlay, hue_overlay)

        # Crear overlay de escala de grises
        if grayscale > 0:
            alpha = int(grayscale * 2)
            gray_overlay = Image.new('RGBA', (width, height), (128, 128, 128, alpha))
            overlay = Image.alpha_composite(overlay, gray_overlay)

        # Crear overlay sepia
        if sepia > 0:
            alpha = int(sepia * 1.5)
            sepia_overlay = Image.new('RGBA', (width, height), (200, 180, 140, alpha))
            overlay = Image.alpha_composite(overlay, sepia_overlay)

        # Crear overlay invertido
        if invert > 0:
            alpha = int(invert * 2)
            invert_overlay = Image.new('RGBA', (width, height), (128, 128, 128, alpha))
            # Usar modo de mezcla diferente para inversión
            overlay = Image.alpha_composite(overlay, invert_overlay)

        # Aplicar opacidad global
        if opacity < 100:
            # Reducir la opacidad de todo el overlay
            overlay_array = np.array(overlay)
            overlay_array[:, :, 3] = (overlay_array[:, :, 3] * opacity / 100).astype(np.uint8)
            overlay = Image.fromarray(overlay_array)

        return overlay
    
    def update_settings(self, new_settings: dict):
        """Actualiza la configuración y refresca el overlay."""
        for key, value in new_settings.items():
            self.config_manager.set_setting(key, value)
        
        # Forzar actualización inmediata
        self.update_overlay()
    
    def set_enabled(self, enabled: bool):
        """Habilita o deshabilita el overlay."""
        self.config_manager.set_setting('enabled', enabled)
        if enabled:
            self.start_overlay()
            self.show()
        else:
            self.stop_overlay()
            self.hide()
    
    def closeEvent(self, event):
        """Maneja el evento de cierre."""
        self.stop_overlay()
        event.accept()
