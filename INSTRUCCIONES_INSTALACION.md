# 📋 Instrucciones de Instalación - Visual Filter

## 🚀 Opción 1: Ejecutar desde Código Fuente (Recomendado para desarrollo)

### Requisitos Previos
1. **Python 3.7 o superior** instalado en tu sistema
   - Descarga desde: https://www.python.org/downloads/
   - Durante la instalación, marca "Add Python to PATH"

2. **pip** (incluido con Python)

### Pasos de Instalación

1. **<PERSON>cargar el proyecto**
   - Descarga todos los archivos del proyecto
   - Extrae en una carpeta (ej: `C:\VisualFilter\`)

2. **Instalación automática**
   - Haz doble clic en `install_and_run.bat`
   - El script instalará las dependencias y ejecutará la aplicación

3. **Instalación manual**
   ```bash
   # Abrir terminal en la carpeta del proyecto
   pip install -r requirements.txt
   python main.py
   ```

## 🎯 Opción 2: Compilar a Ejecutable (.exe)

### Para crear un ejecutable portable

1. **Compilación automática**
   - Haz doble clic en `compile_to_exe.bat`
   - Espera a que termine la compilación

2. **Compilación manual**
   ```bash
   python build_exe.py
   ```

3. **Resultado**
   - El ejecutable estará en `dist\VisualFilter.exe`
   - Es portable (no requiere instalación)
   - Funciona en cualquier PC con Windows 7+

## 🔧 Verificación de Instalación

### Después de instalar, deberías ver:
1. Un icono en la bandeja del sistema (system tray)
2. Mensaje de notificación "Visual Filter iniciado"
3. Posibilidad de abrir el panel con `Ctrl+Shift+F`

### Si algo no funciona:
1. **Ejecuta como administrador**
2. **Verifica que Python está en el PATH**
3. **Comprueba que no hay antivirus bloqueando**

## 📁 Estructura de Archivos Necesarios

```
VisualFilter/
├── main.py                    # ✅ Archivo principal
├── config_manager.py          # ✅ Gestión de configuración
├── visual_filter.py           # ✅ Overlay visual
├── gui_controller.py          # ✅ Panel de control
├── system_tray.py             # ✅ Bandeja del sistema
├── filter_effects.py          # ✅ Efectos de filtros
├── requirements.txt           # ✅ Dependencias
├── build_exe.py              # ✅ Script de compilación
├── install_and_run.bat       # ✅ Instalador automático
├── compile_to_exe.bat        # ✅ Compilador automático
└── assets/                   # 📁 Recursos (se crea automáticamente)
```

## 🐛 Solución de Problemas Comunes

### Error: "Python no está instalado"
- Instala Python desde python.org
- Asegúrate de marcar "Add Python to PATH"
- Reinicia la terminal/cmd

### Error: "pip no reconocido"
- Python no está en el PATH
- Reinstala Python marcando la opción PATH
- O usa: `python -m pip install -r requirements.txt`

### Error: "No se pueden instalar dependencias"
- Ejecuta como administrador
- Actualiza pip: `python -m pip install --upgrade pip`
- Verifica conexión a internet
- Si hay problemas con versiones específicas, usa: `pip install PyQt5 pystray Pillow keyboard numpy pyinstaller`

### La aplicación no aparece
- Busca el icono en la bandeja del sistema (esquina inferior derecha)
- Presiona `Ctrl+Shift+F` para abrir el panel
- Verifica que no hay antivirus bloqueando

### Los filtros no funcionan
- Verifica que están habilitados (checkbox en el panel)
- Comprueba que la opacidad no está en 0%
- Reinicia la aplicación

## 🎮 Primeros Pasos

### 1. Iniciar la aplicación
- Ejecuta `install_and_run.bat` o `python main.py`
- Busca el icono en la bandeja del sistema

### 2. Abrir el panel de control
- Haz clic derecho en el icono → "Abrir Panel de Control"
- O presiona `Ctrl+Shift+F`

### 3. Probar filtros básicos
- Mueve el slider de "Brillo" para ver el efecto
- Ajusta "Temperatura" para colores más cálidos/fríos
- Prueba "Modo Noche" desde el menú del icono

### 4. Guardar tu configuración
- Ajusta los filtros a tu gusto
- En "Perfiles" → escribe un nombre → "Guardar"
- Tu configuración se guarda automáticamente

## 📞 Soporte

### Si necesitas ayuda:
1. **Revisa este archivo** de instrucciones
2. **Comprueba los requisitos** del sistema
3. **Ejecuta como administrador** si hay problemas
4. **Verifica la configuración** de antivirus

### Información del sistema requerida:
- Windows 7 o superior
- 4 GB RAM (recomendado)
- Python 3.7+ (para código fuente)
- Conexión a internet (para instalar dependencias)

---

**¡Disfruta usando Visual Filter! 🎨**
