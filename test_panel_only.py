"""
Versión simplificada para probar solo el panel de control.
"""

import sys
from PyQt5.QtWidgets import QApplication
from config_manager import ConfigManager
from gui_controller import FilterControlPanel

def main():
    """Función principal simplificada."""
    print("=== Test Panel de Control Visual Filter ===")
    
    try:
        # Crear aplicación Qt
        app = QApplication(sys.argv)
        
        # Crear configuración
        config = ConfigManager("test_panel_config.json")
        
        # Crear panel de control
        panel = FilterControlPanel(config)
        
        # Conectar señal para ver cambios
        def on_settings_changed(settings):
            print(f"Configuración cambiada: {settings}")
        
        panel.settings_changed.connect(on_settings_changed)
        
        # Mostrar panel con fuerza
        panel.force_show()
        
        print("✅ Panel de control creado y mostrado")
        print("Deberías ver una ventana con controles de filtros")
        print("Prueba mover los sliders para ver si funcionan")
        print("Cierra la ventana para terminar")
        
        # Ejecutar aplicación
        result = app.exec_()
        
        # Limpiar
        import os
        if os.path.exists("test_panel_config.json"):
            os.remove("test_panel_config.json")
        
        return result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
