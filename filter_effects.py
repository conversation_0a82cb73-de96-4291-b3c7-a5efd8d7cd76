"""
Efectos y filtros visuales para la aplicación.
Contiene las funciones para aplicar diferentes efectos a las imágenes.
"""

import numpy as np
from PIL import Image, ImageEnhance, ImageFilter, ImageOps
import colorsys

class FilterEffects:
    @staticmethod
    def apply_brightness(image: Image.Image, value: int) -> Image.Image:
        """Aplica ajuste de brillo (-100 a 100)."""
        if value == 0:
            return image
        
        factor = 1.0 + (value / 100.0)
        factor = max(0.1, min(3.0, factor))  # Limitar el rango
        enhancer = ImageEnhance.Brightness(image)
        return enhancer.enhance(factor)
    
    @staticmethod
    def apply_contrast(image: Image.Image, value: int) -> Image.Image:
        """Aplica ajuste de contraste (-100 a 100)."""
        if value == 0:
            return image
        
        factor = 1.0 + (value / 100.0)
        factor = max(0.1, min(3.0, factor))  # Limitar el rango
        enhancer = ImageEnhance.Contrast(image)
        return enhancer.enhance(factor)
    
    @staticmethod
    def apply_saturation(image: Image.Image, value: int) -> Image.Image:
        """Aplica ajuste de saturación (-100 a 100)."""
        if value == 0:
            return image
        
        factor = 1.0 + (value / 100.0)
        factor = max(0.0, min(3.0, factor))  # Limitar el rango
        enhancer = ImageEnhance.Color(image)
        return enhancer.enhance(factor)
    
    @staticmethod
    def apply_hue_shift(image: Image.Image, value: int) -> Image.Image:
        """Aplica rotación de tono (-180 a 180)."""
        if value == 0:
            return image
        
        # Convertir a HSV para manipular el tono
        hsv_image = image.convert('HSV')
        hsv_array = np.array(hsv_image)
        
        # Ajustar el canal de tono (H)
        hue_shift = value / 360.0  # Normalizar a 0-1
        hsv_array[:, :, 0] = (hsv_array[:, :, 0] + hue_shift * 255) % 255
        
        # Convertir de vuelta a RGB
        hsv_image = Image.fromarray(hsv_array, 'HSV')
        return hsv_image.convert('RGB')
    
    @staticmethod
    def apply_grayscale(image: Image.Image, value: int) -> Image.Image:
        """Aplica efecto de escala de grises (0 a 100)."""
        if value == 0:
            return image
        
        gray_image = ImageOps.grayscale(image).convert('RGB')
        
        # Mezclar imagen original con versión en grises
        factor = value / 100.0
        return Image.blend(image, gray_image, factor)
    
    @staticmethod
    def apply_sepia(image: Image.Image, value: int) -> Image.Image:
        """Aplica efecto sepia (0 a 100)."""
        if value == 0:
            return image
        
        # Convertir a array numpy para manipulación
        img_array = np.array(image)
        
        # Matriz de transformación sepia
        sepia_filter = np.array([
            [0.393, 0.769, 0.189],
            [0.349, 0.686, 0.168],
            [0.272, 0.534, 0.131]
        ])
        
        # Aplicar filtro sepia
        sepia_img = img_array.dot(sepia_filter.T)
        sepia_img = np.clip(sepia_img, 0, 255).astype(np.uint8)
        sepia_image = Image.fromarray(sepia_img)
        
        # Mezclar con imagen original
        factor = value / 100.0
        return Image.blend(image, sepia_image, factor)
    
    @staticmethod
    def apply_invert(image: Image.Image, value: int) -> Image.Image:
        """Aplica inversión de colores (0 a 100)."""
        if value == 0:
            return image
        
        inverted_image = ImageOps.invert(image)
        
        # Mezclar imagen original con versión invertida
        factor = value / 100.0
        return Image.blend(image, inverted_image, factor)
    
    @staticmethod
    def apply_temperature(image: Image.Image, value: int) -> Image.Image:
        """Aplica ajuste de temperatura de color (-100 a 100)."""
        if value == 0:
            return image
        
        img_array = np.array(image, dtype=np.float32)
        
        if value > 0:  # Más cálido (más rojo/amarillo)
            factor = value / 100.0
            img_array[:, :, 0] *= (1.0 + factor * 0.3)  # Más rojo
            img_array[:, :, 1] *= (1.0 + factor * 0.1)  # Poco más verde
            img_array[:, :, 2] *= (1.0 - factor * 0.2)  # Menos azul
        else:  # Más frío (más azul)
            factor = abs(value) / 100.0
            img_array[:, :, 0] *= (1.0 - factor * 0.2)  # Menos rojo
            img_array[:, :, 1] *= (1.0 - factor * 0.1)  # Poco menos verde
            img_array[:, :, 2] *= (1.0 + factor * 0.3)  # Más azul
        
        img_array = np.clip(img_array, 0, 255).astype(np.uint8)
        return Image.fromarray(img_array)
    
    @staticmethod
    def apply_vignette(image: Image.Image, value: int) -> Image.Image:
        """Aplica efecto viñeta (0 a 100)."""
        if value == 0:
            return image
        
        width, height = image.size
        
        # Crear máscara de viñeta
        center_x, center_y = width // 2, height // 2
        max_distance = np.sqrt(center_x**2 + center_y**2)
        
        # Crear gradiente radial
        y, x = np.ogrid[:height, :width]
        distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        
        # Normalizar distancia y aplicar curva
        normalized_distance = distance / max_distance
        vignette_strength = value / 100.0
        vignette_mask = 1.0 - (normalized_distance * vignette_strength)
        vignette_mask = np.clip(vignette_mask, 0.2, 1.0)  # No hacer completamente negro
        
        # Aplicar máscara a la imagen
        img_array = np.array(image, dtype=np.float32)
        for channel in range(3):
            img_array[:, :, channel] *= vignette_mask
        
        img_array = np.clip(img_array, 0, 255).astype(np.uint8)
        return Image.fromarray(img_array)
    
    @staticmethod
    def apply_blur(image: Image.Image, value: int) -> Image.Image:
        """Aplica desenfoque gaussiano (0 a 10)."""
        if value == 0:
            return image
        
        blur_radius = value / 2.0  # Convertir a radio de desenfoque
        return image.filter(ImageFilter.GaussianBlur(radius=blur_radius))
    
    @staticmethod
    def apply_all_effects(image: Image.Image, settings: dict) -> Image.Image:
        """Aplica todos los efectos en secuencia según la configuración."""
        result = image.copy()
        
        # Aplicar efectos en orden específico para mejores resultados
        result = FilterEffects.apply_brightness(result, settings.get('brightness', 0))
        result = FilterEffects.apply_contrast(result, settings.get('contrast', 0))
        result = FilterEffects.apply_saturation(result, settings.get('saturation', 0))
        result = FilterEffects.apply_hue_shift(result, settings.get('hue', 0))
        result = FilterEffects.apply_temperature(result, settings.get('temperature', 0))
        result = FilterEffects.apply_grayscale(result, settings.get('grayscale', 0))
        result = FilterEffects.apply_sepia(result, settings.get('sepia', 0))
        result = FilterEffects.apply_invert(result, settings.get('invert', 0))
        result = FilterEffects.apply_vignette(result, settings.get('vignette', 0))
        result = FilterEffects.apply_blur(result, settings.get('blur', 0))
        
        return result
