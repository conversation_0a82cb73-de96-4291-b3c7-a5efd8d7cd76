@echo off
echo ==========================================
echo Visual Filter - Compilacion a Ejecutable
echo ==========================================
echo.

echo Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python no esta instalado o no esta en el PATH
    echo Por favor instala Python 3.7 o superior desde python.org
    pause
    exit /b 1
)

echo Python encontrado!
echo.

echo Ejecutando script de compilacion...
python build_exe.py

if errorlevel 1 (
    echo.
    echo ERROR: La compilacion fallo
    echo Revisa los mensajes de error anteriores
    pause
    exit /b 1
)

echo.
echo ==========================================
echo COMPILACION COMPLETADA EXITOSAMENTE!
echo ==========================================
echo.
echo El ejecutable se encuentra en: dist\VisualFilter.exe
echo.
echo Para distribuir la aplicacion:
echo 1. Copia el archivo VisualFilter.exe
echo 2. El ejecutable es portable (no requiere instalacion)
echo 3. Funciona en cualquier PC con Windows 7 o superior
echo.

if exist "dist\VisualFilter.exe" (
    echo ¿Quieres ejecutar la aplicacion ahora? (S/N)
    set /p choice=
    if /i "%choice%"=="S" (
        echo Ejecutando Visual Filter...
        start "" "dist\VisualFilter.exe"
    )
)

pause
