"""
Script para compilar la aplicación Visual Filter a un ejecutable .exe
usando PyInstaller.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_spec_file():
    """Crea el archivo .spec para PyInstaller con configuración personalizada."""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'PIL._tkinter_finder',
        'pystray._win32',
        'keyboard._winkeyboard',
        'PyQt5.sip',
        'numpy.core._methods',
        'numpy.lib.format'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'scipy',
        'pandas',
        'tkinter',
        'unittest',
        'test'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='VisualFilter',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Sin ventana de consola
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
'''
    
    with open('visual_filter.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content.strip())
    
    print("Archivo .spec creado: visual_filter.spec")

def create_icon():
    """Crea un icono básico para la aplicación."""
    try:
        from PIL import Image, ImageDraw
        
        # Crear directorio assets si no existe
        os.makedirs('assets', exist_ok=True)
        
        # Crear imagen de icono 256x256
        size = 256
        image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        # Dibujar icono simple
        center = size // 2
        radius = size // 3
        
        # Círculo exterior azul
        draw.ellipse([center-radius, center-radius, center+radius, center+radius], 
                    fill=(100, 150, 255, 255), outline=(50, 100, 200, 255), width=8)
        
        # Círculo interior
        inner_radius = radius - 20
        draw.ellipse([center-inner_radius, center-inner_radius, 
                     center+inner_radius, center+inner_radius], 
                    fill=(150, 200, 255, 200))
        
        # Punto central
        dot_radius = 12
        draw.ellipse([center-dot_radius, center-dot_radius, 
                     center+dot_radius, center+dot_radius], 
                    fill=(255, 255, 255, 255))
        
        # Guardar como ICO
        image.save('assets/icon.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        print("Icono creado: assets/icon.ico")
        
    except ImportError:
        print("PIL no disponible, saltando creación de icono.")
    except Exception as e:
        print(f"Error creando icono: {e}")

def install_dependencies():
    """Instala las dependencias necesarias."""
    print("Instalando dependencias...")
    
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True, capture_output=True, text=True)
        print("Dependencias instaladas correctamente.")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error instalando dependencias: {e}")
        print(f"Salida del error: {e.stderr}")
        return False

def build_executable():
    """Compila la aplicación a ejecutable."""
    print("Compilando aplicación...")
    
    try:
        # Limpiar builds anteriores
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        if os.path.exists('build'):
            shutil.rmtree('build')
        
        # Ejecutar PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'visual_filter.spec'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("Compilación exitosa!")
            print("Ejecutable creado en: dist/VisualFilter.exe")
            
            # Verificar que el archivo existe
            exe_path = Path('dist/VisualFilter.exe')
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"Tamaño del ejecutable: {size_mb:.1f} MB")
                return True
            else:
                print("Error: El ejecutable no se creó correctamente.")
                return False
        else:
            print("Error en la compilación:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"Error durante la compilación: {e}")
        return False

def create_readme():
    """Crea un archivo README con instrucciones."""
    readme_content = """
# Visual Filter - Filtro Visual Global para Windows

## Descripción
Visual Filter es una aplicación que permite aplicar filtros visuales personalizables sobre toda la pantalla de Windows.

## Características
- Filtros de brillo, contraste, saturación, tono
- Efectos especiales: sepia, escala de grises, inversión
- Perfiles personalizables
- Atajos de teclado globales
- Minimización en bandeja del sistema

## Atajos de Teclado
- Ctrl+Shift+F: Abrir/cerrar panel de control
- Ctrl+Shift+T: Alternar filtros on/off
- Ctrl+Shift+R: Restablecer configuración
- Ctrl+Shift+1: Modo noche
- Ctrl+Shift+2: Modo lectura
- Ctrl+Shift+3: Modo cálido

## Uso
1. Ejecuta VisualFilter.exe
2. Aparecerá un icono en la bandeja del sistema
3. Haz clic derecho en el icono para acceder al menú
4. Usa "Abrir Panel de Control" para ajustar filtros
5. Los ajustes se guardan automáticamente

## Requisitos del Sistema
- Windows 7 o superior
- 4 GB de RAM recomendados
- Tarjeta gráfica compatible con DirectX 9

## Solución de Problemas
- Si la aplicación no inicia, ejecuta como administrador
- Si los filtros no se aplican, verifica que no haya otras aplicaciones de overlay activas
- Para desinstalar, simplemente elimina el archivo .exe y la carpeta de configuración

## Archivos de Configuración
La configuración se guarda en: visual_filter_config.json

Desarrollado con Python, PyQt5 y pystray.
"""
    
    with open('dist/README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content.strip())
    
    print("README creado: dist/README.txt")

def main():
    """Función principal del script de compilación."""
    print("=== Visual Filter - Script de Compilación ===")
    print()
    
    # Verificar que estamos en el directorio correcto
    if not os.path.exists('main.py'):
        print("Error: No se encontró main.py. Ejecuta este script desde el directorio del proyecto.")
        return 1
    
    # Paso 1: Instalar dependencias
    if not install_dependencies():
        print("Error: No se pudieron instalar las dependencias.")
        return 1
    
    # Paso 2: Crear icono
    create_icon()
    
    # Paso 3: Crear archivo .spec
    create_spec_file()
    
    # Paso 4: Compilar
    if not build_executable():
        print("Error: La compilación falló.")
        return 1
    
    # Paso 5: Crear README
    if os.path.exists('dist'):
        create_readme()
    
    print()
    print("=== Compilación Completada ===")
    print("El ejecutable está listo en: dist/VisualFilter.exe")
    print()
    print("Para distribuir:")
    print("1. Copia el archivo VisualFilter.exe")
    print("2. Incluye el README.txt si es necesario")
    print("3. El ejecutable es portable y no requiere instalación")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
