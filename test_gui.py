"""
Script de prueba para verificar que la interfaz gráfica funciona correctamente.
"""

import sys
from PyQt5.QtWidgets import QApplication
from config_manager import ConfigManager
from gui_controller import FilterControlPanel

def test_gui():
    """Prueba la interfaz gráfica del panel de control."""
    print("Probando interfaz gráfica...")
    
    try:
        # Crear aplicación Qt
        app = QApplication(sys.argv)
        
        # Crear configuración
        config = ConfigManager("test_gui_config.json")
        
        # Crear panel de control
        panel = FilterControlPanel(config)
        panel.show()
        
        print("✅ Panel de control creado correctamente")
        print("✅ Interfaz gráfica funcionando")
        print("\nEl panel de control debería estar visible.")
        print("Puedes probar los sliders y controles.")
        print("Cierra la ventana para terminar la prueba.")
        
        # Ejecutar aplicación
        result = app.exec_()
        
        # Limpiar archivo de prueba
        import os
        if os.path.exists("test_gui_config.json"):
            os.remove("test_gui_config.json")
        
        print("Prueba de GUI completada.")
        return result
        
    except Exception as e:
        print(f"❌ Error en la prueba de GUI: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(test_gui())
