# 🎨 Visual Filter - Filtro Visual Global para Windows

Una aplicación de escritorio en Python que funciona como un filtro visual personalizable que se superpone a toda la pantalla de Windows.

## ✨ Características

### 🎛️ Controles de Filtros
- **Brillo** (-100 a 100): Ajusta la luminosidad general
- **Contraste** (-100 a 100): Modifica la diferencia entre luces y sombras
- **Tono** (-180 a 180): Rotación del espectro de colores
- **Saturación** (-100 a 100): Intensidad de los colores
- **Temperatura** (-100 a 100): Colores cálidos (rojizos) o fríos (azulados)
- **Opacidad** (0 a 100): Transparencia del filtro

### 🎭 Efectos Especiales
- **Escala de Grises** (0 a 100): Convierte la imagen a blanco y negro
- **Sepia** (0 a 100): Efecto vintage en tonos marrones
- **Invertido** (0 a 100): Invierte los colores
- **Viñeta** (0 a 100): Oscurece los bordes de la pantalla
- **Desenfoque** (0 a 10): Aplica desenfoque gaussiano

### 🔧 Funcionalidades
- **Overlay Global**: Se superpone a todas las aplicaciones
- **Bandeja del Sistema**: Minimización en system tray
- **Perfiles**: Guarda y carga configuraciones personalizadas
- **Atajos Globales**: Control mediante teclado
- **Persistencia**: Recuerda la última configuración
- **Presets**: Modos predefinidos (Noche, Lectura, Cálido)

## ⌨️ Atajos de Teclado

| Atajo | Función |
|-------|---------|
| `Ctrl+Shift+F` | Abrir/cerrar panel de control |
| `Ctrl+Shift+T` | Alternar filtros on/off |
| `Ctrl+Shift+R` | Restablecer configuración |
| `Ctrl+Shift+1` | Modo noche |
| `Ctrl+Shift+2` | Modo lectura |
| `Ctrl+Shift+3` | Modo cálido |

## 🚀 Instalación y Uso

### Opción 1: Versión Simplificada (Recomendada)

1. **Instalar dependencias**:
   ```bash
   pip install -r requirements.txt
   ```
2. **Ejecutar versión simplificada**:
   ```bash
   python visual_filter_simple.py
   ```
   - ✅ Panel de control siempre visible
   - ✅ Sin problemas de visibilidad
   - ✅ Todos los filtros funcionales

### Opción 2: Versión Completa (Con System Tray)

1. **Ejecutar aplicación completa**:
   ```bash
   python main.py
   ```
   - ✅ Bandeja del sistema
   - ✅ Atajos de teclado globales
   - ✅ Menú contextual completo

### Opción 3: Compilar a ejecutable

1. **Ejecutar el script de compilación**:
   ```bash
   python build_exe.py
   ```
2. **Encontrar el ejecutable** en `dist/VisualFilter.exe`
3. **Ejecutar** el archivo .exe (portable, no requiere instalación)

## 🛠️ Dependencias

```
PyQt5==5.15.10
pystray==0.19.5
Pillow==10.1.0
keyboard==0.13.5
pyinstaller==6.3.0
numpy==1.24.3
```

## 📁 Estructura del Proyecto

```
visual-filter/
├── main.py                 # Punto de entrada principal
├── config_manager.py       # Gestión de configuraciones
├── visual_filter.py        # Overlay visual principal
├── gui_controller.py       # Panel de control GUI
├── system_tray.py          # Bandeja del sistema
├── filter_effects.py       # Efectos y filtros
├── build_exe.py           # Script de compilación
├── requirements.txt        # Dependencias
├── README.md              # Este archivo
└── assets/                # Recursos (iconos, etc.)
```

## 🎯 Uso Detallado

### Primera Ejecución
1. Al iniciar, aparece un icono en la bandeja del sistema
2. Haz clic derecho en el icono para ver el menú contextual
3. Selecciona "Abrir Panel de Control" para ajustar filtros

### Panel de Control
- **Controles Básicos**: Brillo, contraste, opacidad
- **Controles de Color**: Tono, saturación, temperatura
- **Efectos Especiales**: Grises, sepia, invertido, viñeta, desenfoque
- **Perfiles**: Guarda, carga y elimina configuraciones personalizadas

### Menú de Bandeja del Sistema
- **Abrir Panel de Control**: Muestra la ventana de ajustes
- **Alternar Filtros**: Habilita/deshabilita todos los filtros
- **Perfiles**: Acceso rápido a presets y funciones de perfil
- **Acerca de**: Información de la aplicación
- **Salir**: Cierra completamente la aplicación

## 🔧 Configuración

La configuración se guarda automáticamente en `visual_filter_config.json` e incluye:
- Todos los valores de filtros
- Perfiles personalizados guardados
- Posición de la ventana
- Estado de habilitación

## 🎨 Presets Incluidos

### Modo Noche
- Brillo: -30
- Temperatura: +40 (más cálido)
- Opacidad: 80%
- Contraste: -10

### Modo Lectura
- Brillo: +10
- Temperatura: +20
- Contraste: +15
- Sepia: 20%

### Modo Cálido
- Temperatura: +60
- Brillo: -10
- Saturación: -20

## 🐛 Solución de Problemas

### La aplicación no inicia
- Ejecuta como administrador
- Verifica que todas las dependencias están instaladas
- Comprueba que no hay otras aplicaciones de overlay conflictivas

### Los filtros no se aplican
- Verifica que los filtros están habilitados (checkbox en el panel)
- Comprueba que la opacidad no está en 0%
- Reinicia la aplicación

### Rendimiento lento
- Reduce la intensidad de los efectos
- Desactiva el desenfoque si está activado
- Cierra otras aplicaciones que usen overlay

### El icono no aparece en la bandeja
- Verifica que la bandeja del sistema está habilitada en Windows
- Reinicia el explorador de Windows
- Ejecuta la aplicación como administrador

## 🔒 Requisitos del Sistema

- **SO**: Windows 7 o superior
- **RAM**: 4 GB recomendados
- **Procesador**: Dual-core 2.0 GHz o superior
- **Gráficos**: Compatible con DirectX 9
- **Espacio**: 50 MB libres

## 📝 Notas de Desarrollo

### Tecnologías Utilizadas
- **PyQt5**: Interfaz gráfica y overlay
- **pystray**: Bandeja del sistema
- **PIL/Pillow**: Procesamiento de imágenes
- **keyboard**: Atajos globales
- **numpy**: Operaciones matemáticas
- **PyInstaller**: Compilación a ejecutable

### Arquitectura
- **Patrón MVC**: Separación clara entre modelo, vista y controlador
- **Señales Qt**: Comunicación entre componentes
- **Threading**: Operaciones no bloqueantes
- **Singleton Config**: Gestión centralizada de configuración

## 🤝 Contribuciones

Las contribuciones son bienvenidas. Por favor:
1. Fork el proyecto
2. Crea una rama para tu feature
3. Commit tus cambios
4. Push a la rama
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo LICENSE para más detalles.

## 🙏 Agradecimientos

- Comunidad de PyQt5 por la excelente documentación
- Desarrolladores de pystray por la integración con system tray
- Comunidad de Python por las librerías utilizadas

---

**Desarrollado con ❤️ usando Python**
