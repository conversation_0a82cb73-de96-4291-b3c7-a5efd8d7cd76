"""
Test específico para el problema de minimizar/mostrar el panel.
"""

import sys
from PyQt5.QtWidgets import QApplication, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer
from config_manager import ConfigManager
from gui_controller import FilterControlPanel

def test_minimize_show():
    """Prueba el comportamiento de minimizar y mostrar."""
    print("=== Test Minimizar/Mostrar Panel ===")
    
    app = QApplication(sys.argv)
    config = ConfigManager("test_minimize_config.json")
    panel = FilterControlPanel(config)
    
    # Crear ventana de control para las pruebas
    control_window = QWidget()
    control_window.setWindowTitle("Control de Pruebas")
    control_window.setGeometry(50, 50, 300, 200)
    
    layout = QVBoxLayout(control_window)
    
    # Botón para mostrar panel
    show_btn = QPushButton("Mostrar Panel (force_show)")
    show_btn.clicked.connect(lambda: test_show_panel(panel, "force_show"))
    layout.addWidget(show_btn)
    
    # Botón para mostrar panel (método alternativo)
    show_alt_btn = QPushButton("Mostrar Panel (show_and_focus)")
    show_alt_btn.clicked.connect(lambda: test_show_panel(panel, "show_and_focus"))
    layout.addWidget(show_alt_btn)
    
    # Botón para ocultar panel
    hide_btn = QPushButton("Ocultar Panel")
    hide_btn.clicked.connect(lambda: test_hide_panel(panel))
    layout.addWidget(hide_btn)
    
    # Botón para minimizar panel
    minimize_btn = QPushButton("Minimizar Panel")
    minimize_btn.clicked.connect(lambda: test_minimize_panel(panel))
    layout.addWidget(minimize_btn)
    
    # Botón para estado del panel
    status_btn = QPushButton("Estado del Panel")
    status_btn.clicked.connect(lambda: print_panel_status(panel))
    layout.addWidget(status_btn)
    
    control_window.show()
    
    # Mostrar panel inicialmente
    print("Mostrando panel inicialmente...")
    panel.force_show()
    
    print("Ventana de control creada. Usa los botones para probar.")
    print("Cierra la ventana de control para terminar.")
    
    result = app.exec_()
    
    # Limpiar
    import os
    if os.path.exists("test_minimize_config.json"):
        os.remove("test_minimize_config.json")
    
    return result

def test_show_panel(panel, method):
    """Prueba mostrar el panel con diferentes métodos."""
    print(f"\n=== Probando mostrar panel con {method} ===")
    print_panel_status(panel)
    
    if method == "force_show":
        result = panel.force_show()
        print(f"Resultado de force_show: {result}")
    elif method == "show_and_focus":
        panel.show_and_focus()
    
    print_panel_status(panel)
    print("=== Fin prueba mostrar ===\n")

def test_hide_panel(panel):
    """Prueba ocultar el panel."""
    print("\n=== Probando ocultar panel ===")
    print_panel_status(panel)
    
    panel.hide()
    
    print_panel_status(panel)
    print("=== Fin prueba ocultar ===\n")

def test_minimize_panel(panel):
    """Prueba minimizar el panel."""
    print("\n=== Probando minimizar panel ===")
    print_panel_status(panel)
    
    panel.showMinimized()
    
    print_panel_status(panel)
    print("=== Fin prueba minimizar ===\n")

def print_panel_status(panel):
    """Imprime el estado actual del panel."""
    visible = panel.isVisible()
    minimized = panel.isMinimized()
    active = panel.isActiveWindow()
    
    print(f"Estado del panel:")
    print(f"  - Visible: {visible}")
    print(f"  - Minimizado: {minimized}")
    print(f"  - Activo: {active}")
    print(f"  - Posición: {panel.pos()}")
    print(f"  - Tamaño: {panel.size()}")

if __name__ == "__main__":
    sys.exit(test_minimize_show())
