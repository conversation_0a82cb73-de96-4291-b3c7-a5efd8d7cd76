"""
Test específico para probar el comportamiento de mostrar/ocultar del panel.
"""

import sys
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from config_manager import ConfigManager
from gui_controller import <PERSON>lterControlPanel

def test_show_hide():
    """Prueba el comportamiento de mostrar/ocultar."""
    print("=== Test Mostrar/Ocultar Panel ===")
    
    app = QApplication(sys.argv)
    config = ConfigManager("test_show_hide_config.json")
    panel = FilterControlPanel(config)
    
    print("1. Mostrando panel inicialmente...")
    panel.force_show()
    print(f"   Estado: Visible={panel.isVisible()}, Minimizado={panel.isMinimized()}")
    
    # Timer para ocultar después de 3 segundos
    def hide_panel():
        print("2. Ocultando panel...")
        panel.hide()
        print(f"   Estado: Visible={panel.isVisible()}, Minimizado={panel.isMinimized()}")
        
        # Timer para mostrar de nuevo después de 2 segundos
        QTimer.singleShot(2000, show_panel_again)
    
    def show_panel_again():
        print("3. Mostrando panel de nuevo...")
        panel.force_show()
        print(f"   Estado: Visible={panel.isVisible()}, Minimizado={panel.isMinimized()}")
        
        # Timer para cerrar después de 3 segundos
        QTimer.singleShot(3000, app.quit)
    
    # Programar la secuencia
    QTimer.singleShot(3000, hide_panel)
    
    print("Ejecutando secuencia de prueba...")
    print("- Mostrará el panel por 3 segundos")
    print("- Lo ocultará por 2 segundos") 
    print("- Lo mostrará de nuevo por 3 segundos")
    print("- Luego cerrará automáticamente")
    
    result = app.exec_()
    
    # Limpiar
    import os
    if os.path.exists("test_show_hide_config.json"):
        os.remove("test_show_hide_config.json")
    
    return result

if __name__ == "__main__":
    sys.exit(test_show_hide())
