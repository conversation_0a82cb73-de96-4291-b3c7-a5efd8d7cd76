"""
Versión simplificada de Visual Filter que solo muestra el panel de control
sin system tray para evitar problemas de visibilidad.
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

# Importar módulos locales
from config_manager import ConfigManager
from visual_filter import VisualFilterOverlay
from gui_controller import FilterControlPanel

class SimpleVisualFilter:
    """Versión simplificada del filtro visual."""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.overlay = None
        self.control_panel = None
        self.app = None
        
    def initialize(self):
        """Inicializa la aplicación simplificada."""
        try:
            # Crear aplicación Qt
            self.app = QApplication(sys.argv)
            self.app.setQuitOnLastWindowClosed(True)  # Cerrar cuando se cierre la ventana
            
            # Crear overlay visual
            self.overlay = VisualFilterOverlay(self.config_manager)
            
            # Crear panel de control
            self.control_panel = FilterControlPanel(self.config_manager)
            self.control_panel.settings_changed.connect(self.on_settings_changed)
            
            # Configurar posición del panel
            pos = self.config_manager.get_setting('window_position')
            if pos:
                self.control_panel.move(pos['x'], pos['y'])
            
            # Mostrar el panel inmediatamente
            self.control_panel.force_show()
            
            # Mostrar mensaje de bienvenida
            QTimer.singleShot(1000, self.show_welcome_message)
            
            print("✅ Visual Filter Simple iniciado correctamente")
            print("📋 Panel de control visible")
            print("🎨 Overlay activo")
            print("💡 Prueba mover los sliders para ver los efectos")
            
            return True
            
        except Exception as e:
            print(f"❌ Error inicializando aplicación: {e}")
            return False
    
    def show_welcome_message(self):
        """Muestra mensaje de bienvenida."""
        msg = QMessageBox()
        msg.setWindowTitle("🎨 Visual Filter")
        msg.setText("¡Bienvenido a Visual Filter!")
        msg.setInformativeText(
            "• Usa los sliders para ajustar los filtros visuales\n"
            "• Los cambios se aplican en tiempo real\n"
            "• Puedes guardar perfiles personalizados\n"
            "• Los ajustes se guardan automáticamente"
        )
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec_()
    
    def on_settings_changed(self, settings: dict):
        """Maneja cambios en la configuración."""
        if self.overlay:
            self.overlay.update_settings(settings)
        print(f"🔧 Configuración actualizada: {settings}")
    
    def run(self):
        """Ejecuta la aplicación."""
        if not self.initialize():
            return 1
        
        try:
            # Ejecutar aplicación
            return self.app.exec_()
            
        except KeyboardInterrupt:
            print("\n🔄 Cerrando aplicación...")
            return 0
        except Exception as e:
            print(f"❌ Error ejecutando aplicación: {e}")
            return 1

def main():
    """Función principal."""
    print("=== Visual Filter Simple ===")
    print("Versión simplificada para pruebas")
    print()
    
    # Verificar que estamos en Windows
    if os.name != 'nt':
        print("⚠️ Esta aplicación está diseñada para Windows.")
        return 1
    
    # Crear y ejecutar aplicación
    app = SimpleVisualFilter()
    return app.run()

if __name__ == "__main__":
    sys.exit(main())
