"""
Aplicación principal de Visual Filter.
Filtro visual global personalizable para Windows.
"""

import sys
import os
import signal
import threading
import time
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer, pyqtSignal, QObject
import keyboard

# Importar módulos locales
from config_manager import ConfigManager
from visual_filter import VisualFilterOverlay
from gui_controller import FilterControlPanel
from system_tray import SystemTrayManager

class VisualFilterApp(QObject):
    """Controlador principal de la aplicación."""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.overlay = None
        self.control_panel = None
        self.system_tray = None
        self.app = None
        self.hotkey_thread = None
        self.is_running = True
        
    def initialize(self):
        """Inicializa todos los componentes de la aplicación."""
        try:
            # Crear aplicación Qt
            self.app = QApplication(sys.argv)
            self.app.setQuitOnLastWindowClosed(False)  # No cerrar al cerrar ventanas
            
            # Crear overlay visual
            self.overlay = VisualFilterOverlay(self.config_manager)
            
            # Crear panel de control
            self.control_panel = FilterControlPanel(self.config_manager)
            self.control_panel.settings_changed.connect(self.on_settings_changed)
            self.control_panel.toggle_overlay.connect(self.toggle_overlay)
            
            # Crear system tray
            self.system_tray = SystemTrayManager(self)
            
            # Configurar posición inicial del panel
            pos = self.config_manager.get_setting('window_position')
            if pos:
                self.control_panel.move(pos['x'], pos['y'])

            # Mostrar el panel de control al inicio para verificar que funciona
            self.control_panel.force_show()

            # Iniciar system tray
            self.system_tray.start()

            # Configurar atajos de teclado globales
            self.setup_global_hotkeys()

            # Mostrar notificación de inicio
            if self.system_tray.is_tray_available():
                self.system_tray.show_notification(
                    "Visual Filter",
                    "Aplicación iniciada. Panel de control visible."
                )

            print("Visual Filter iniciado correctamente.")
            print("El panel de control debería estar visible.")
            print("Presiona Ctrl+Shift+F para abrir/cerrar el panel de control.")
            print("Presiona Ctrl+Shift+T para alternar los filtros.")
            
            return True
            
        except Exception as e:
            print(f"Error inicializando aplicación: {e}")
            return False
    
    def setup_global_hotkeys(self):
        """Configura los atajos de teclado globales."""
        def hotkey_worker():
            try:
                # Registrar atajos
                keyboard.add_hotkey('ctrl+shift+f', self.toggle_control_panel)
                keyboard.add_hotkey('ctrl+shift+t', self.toggle_overlay)
                keyboard.add_hotkey('ctrl+shift+r', self.reset_settings)
                keyboard.add_hotkey('ctrl+shift+1', lambda: self.load_quick_preset('night_mode'))
                keyboard.add_hotkey('ctrl+shift+2', lambda: self.load_quick_preset('reading_mode'))
                keyboard.add_hotkey('ctrl+shift+3', lambda: self.load_quick_preset('warm_mode'))
                
                # Mantener el hilo activo
                while self.is_running:
                    time.sleep(0.1)
                    
            except Exception as e:
                print(f"Error configurando atajos de teclado: {e}")
        
        # Ejecutar en hilo separado
        self.hotkey_thread = threading.Thread(target=hotkey_worker, daemon=True)
        self.hotkey_thread.start()
    
    def run(self):
        """Ejecuta la aplicación."""
        if not self.initialize():
            return 1
        
        try:
            # Configurar manejo de señales para cierre limpio
            signal.signal(signal.SIGINT, self.signal_handler)
            signal.signal(signal.SIGTERM, self.signal_handler)
            
            # Ejecutar aplicación
            return self.app.exec_()
            
        except KeyboardInterrupt:
            print("\nCerrando aplicación...")
            self.quit_application()
            return 0
        except Exception as e:
            print(f"Error ejecutando aplicación: {e}")
            return 1
    
    def signal_handler(self, signum, frame):
        """Maneja señales del sistema para cierre limpio."""
        print(f"\nSeñal {signum} recibida. Cerrando aplicación...")
        self.quit_application()
    
    def on_settings_changed(self, settings: dict):
        """Maneja cambios en la configuración."""
        if self.overlay:
            self.overlay.update_settings(settings)
        
        # Actualizar icono del system tray
        if self.system_tray:
            enabled = settings.get('enabled', True)
            self.system_tray.update_icon(enabled)
    
    def show_control_panel(self):
        """Muestra el panel de control."""
        if self.control_panel:
            print("Intentando mostrar panel de control...")
            self.control_panel.force_show()
            print(f"Panel estado después de mostrar - Visible: {self.control_panel.isVisible()}")
        else:
            print("Error: No hay panel de control disponible")
    
    def hide_control_panel(self):
        """Oculta el panel de control."""
        if self.control_panel:
            # Guardar posición antes de ocultar
            pos = self.control_panel.pos()
            self.config_manager.set_setting('window_position', {'x': pos.x(), 'y': pos.y()})
            self.control_panel.hide()
    
    def toggle_control_panel(self):
        """Alterna la visibilidad del panel de control."""
        if self.control_panel:
            if self.control_panel.isVisible() and not self.control_panel.isMinimized():
                print("Ocultando panel de control")
                self.hide_control_panel()
            else:
                print("Mostrando panel de control")
                self.show_control_panel()
    
    def toggle_overlay(self):
        """Alterna el estado del overlay."""
        if self.overlay:
            current_state = self.config_manager.get_setting('enabled')
            new_state = not current_state
            self.config_manager.set_setting('enabled', new_state)
            self.overlay.set_enabled(new_state)
            
            # Actualizar checkbox en el panel
            if self.control_panel:
                self.control_panel.enable_checkbox.setChecked(new_state)
            
            # Actualizar icono
            if self.system_tray:
                self.system_tray.update_icon(new_state)
            
            # Mostrar notificación
            status = "habilitados" if new_state else "deshabilitados"
            if self.system_tray:
                self.system_tray.show_notification("Visual Filter", f"Filtros {status}")
    
    def reset_settings(self):
        """Restablece todos los ajustes."""
        if self.config_manager:
            self.config_manager.reset_to_defaults()
            
            # Actualizar interfaz
            if self.control_panel:
                self.control_panel.load_current_settings()
            
            # Actualizar overlay
            if self.overlay:
                self.overlay.update_settings(self.config_manager.current_settings)
            
            # Mostrar notificación
            if self.system_tray:
                self.system_tray.show_notification("Visual Filter", "Configuración restablecida")
    
    def save_profile(self, name: str):
        """Guarda un perfil con el nombre especificado."""
        if self.config_manager.save_profile(name):
            if self.system_tray:
                self.system_tray.show_notification("Visual Filter", f"Perfil '{name}' guardado")
            return True
        return False
    
    def load_quick_preset(self, preset_name: str):
        """Carga un preset rápido."""
        if self.system_tray:
            self.system_tray.load_preset(preset_name)
    
    def apply_preset(self, settings: dict):
        """Aplica un preset de configuración."""
        for key, value in settings.items():
            self.config_manager.set_setting(key, value)
        
        # Actualizar interfaz
        if self.control_panel:
            self.control_panel.load_current_settings()
        
        # Actualizar overlay
        if self.overlay:
            self.overlay.update_settings(self.config_manager.current_settings)
    
    def quit_application(self):
        """Cierra la aplicación completamente."""
        print("Cerrando Visual Filter...")
        
        self.is_running = False
        
        # Guardar configuración final
        if self.control_panel:
            pos = self.control_panel.pos()
            self.config_manager.set_setting('window_position', {'x': pos.x(), 'y': pos.y()})
        
        # Cerrar componentes
        if self.overlay:
            self.overlay.close()
        
        if self.control_panel:
            self.control_panel.close()
        
        if self.system_tray:
            self.system_tray.stop()
        
        # Limpiar atajos de teclado
        try:
            keyboard.unhook_all()
        except:
            pass
        
        # Cerrar aplicación Qt
        if self.app:
            self.app.quit()
        
        print("Visual Filter cerrado.")

def main():
    """Función principal."""
    # Verificar que estamos en Windows
    if os.name != 'nt':
        print("Esta aplicación está diseñada para Windows.")
        return 1
    
    # Crear y ejecutar aplicación
    app = VisualFilterApp()
    return app.run()

if __name__ == "__main__":
    sys.exit(main())
