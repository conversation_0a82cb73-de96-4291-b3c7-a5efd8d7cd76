"""
Gestor de configuraciones para la aplicación de filtro visual.
Maneja la persistencia de ajustes y perfiles.
"""

import json
import os
from typing import Dict, Any, List

class ConfigManager:
    def __init__(self, config_file: str = "visual_filter_config.json"):
        self.config_file = config_file
        self.default_settings = {
            "brightness": 0,      # -100 a 100
            "contrast": 0,        # -100 a 100
            "hue": 0,            # -180 a 180
            "saturation": 0,      # -100 a 100
            "grayscale": 0,       # 0 a 100
            "sepia": 0,           # 0 a 100
            "invert": 0,          # 0 a 100
            "opacity": 100,       # 0 a 100
            "temperature": 0,     # -100 a 100 (frío a c<PERSON>lido)
            "vignette": 0,        # 0 a 100
            "blur": 0,            # 0 a 10
            "enabled": True,
            "window_position": {"x": 100, "y": 100},
            "profiles": {}
        }
        self.current_settings = self.load_settings()
    
    def load_settings(self) -> Dict[str, Any]:
        """Carga la configuración desde el archivo JSON."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    # Combinar con valores por defecto para nuevas opciones
                    settings = self.default_settings.copy()
                    settings.update(loaded_settings)
                    return settings
            else:
                return self.default_settings.copy()
        except Exception as e:
            print(f"Error cargando configuración: {e}")
            return self.default_settings.copy()
    
    def save_settings(self) -> bool:
        """Guarda la configuración actual al archivo JSON."""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_settings, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error guardando configuración: {e}")
            return False
    
    def get_setting(self, key: str) -> Any:
        """Obtiene un valor de configuración específico."""
        return self.current_settings.get(key, self.default_settings.get(key))
    
    def set_setting(self, key: str, value: Any) -> None:
        """Establece un valor de configuración específico."""
        self.current_settings[key] = value
        self.save_settings()
    
    def reset_to_defaults(self) -> None:
        """Restablece todos los valores a los predeterminados."""
        profiles = self.current_settings.get("profiles", {})
        self.current_settings = self.default_settings.copy()
        self.current_settings["profiles"] = profiles
        self.save_settings()
    
    def save_profile(self, name: str) -> bool:
        """Guarda el perfil actual con el nombre especificado."""
        try:
            if "profiles" not in self.current_settings:
                self.current_settings["profiles"] = {}
            
            profile_data = {
                "brightness": self.current_settings["brightness"],
                "contrast": self.current_settings["contrast"],
                "hue": self.current_settings["hue"],
                "saturation": self.current_settings["saturation"],
                "grayscale": self.current_settings["grayscale"],
                "sepia": self.current_settings["sepia"],
                "invert": self.current_settings["invert"],
                "opacity": self.current_settings["opacity"],
                "temperature": self.current_settings["temperature"],
                "vignette": self.current_settings["vignette"],
                "blur": self.current_settings["blur"]
            }
            
            self.current_settings["profiles"][name] = profile_data
            self.save_settings()
            return True
        except Exception as e:
            print(f"Error guardando perfil: {e}")
            return False
    
    def load_profile(self, name: str) -> bool:
        """Carga un perfil guardado."""
        try:
            if "profiles" in self.current_settings and name in self.current_settings["profiles"]:
                profile_data = self.current_settings["profiles"][name]
                for key, value in profile_data.items():
                    self.current_settings[key] = value
                self.save_settings()
                return True
            return False
        except Exception as e:
            print(f"Error cargando perfil: {e}")
            return False
    
    def get_profiles(self) -> List[str]:
        """Obtiene la lista de perfiles guardados."""
        return list(self.current_settings.get("profiles", {}).keys())
    
    def delete_profile(self, name: str) -> bool:
        """Elimina un perfil guardado."""
        try:
            if "profiles" in self.current_settings and name in self.current_settings["profiles"]:
                del self.current_settings["profiles"][name]
                self.save_settings()
                return True
            return False
        except Exception as e:
            print(f"Error eliminando perfil: {e}")
            return False
