"""
Test simple para verificar que el panel de control se muestra correctamente.
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QSlider
from PyQt5.QtCore import Qt

def test_simple_window():
    """Prueba una ventana simple de PyQt5."""
    print("Creando ventana simple de prueba...")
    
    app = QApplication(sys.argv)
    
    # Crear ventana simple
    window = QMainWindow()
    window.setWindowTitle("Test Visual Filter - Panel Simple")
    window.setGeometry(200, 200, 400, 300)
    
    # Widget central
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    
    # Layout
    layout = QVBoxLayout(central_widget)
    
    # Título
    title = QLabel("🎨 Test Panel Visual Filter")
    title.setAlignment(Qt.AlignCenter)
    layout.addWidget(title)
    
    # Slider de prueba
    slider_label = QLabel("Brillo:")
    layout.addWidget(slider_label)
    
    slider = QSlider(Qt.Horizontal)
    slider.setMinimum(-100)
    slider.setMaximum(100)
    slider.setValue(0)
    layout.addWidget(slider)
    
    # Botón de prueba
    button = QPushButton("Botón de Prueba")
    layout.addWidget(button)
    
    # Mostrar ventana
    window.show()
    window.raise_()
    window.activateWindow()
    
    print("✅ Ventana simple creada y mostrada")
    print("Si puedes ver la ventana, PyQt5 funciona correctamente")
    print("Cierra la ventana para continuar")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(test_simple_window())
