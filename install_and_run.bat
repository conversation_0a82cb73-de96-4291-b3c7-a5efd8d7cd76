@echo off
echo ========================================
echo Visual Filter - Instalacion y Ejecucion
echo ========================================
echo.

echo Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python no esta instalado o no esta en el PATH
    echo Por favor instala Python 3.7 o superior desde python.org
    pause
    exit /b 1
)

echo Python encontrado!
echo.

echo Instalando dependencias...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: No se pudieron instalar las dependencias
    pause
    exit /b 1
)

echo.
echo Dependencias instaladas correctamente!
echo.

echo Iniciando Visual Filter...
echo.
echo INSTRUCCIONES:
echo - La aplicacion se minimizara en la bandeja del sistema
echo - Haz clic derecho en el icono para ver opciones
echo - Presiona Ctrl+Shift+F para abrir el panel de control
echo - Presiona Ctrl+Shift+T para alternar filtros
echo.

python main.py

echo.
echo Visual Filter cerrado.
pause
