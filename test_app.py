"""
Script de prueba simple para verificar que todos los componentes funcionan.
"""

import sys
import os

def test_imports():
    """Prueba que todas las importaciones funcionen."""
    print("Probando importaciones...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando PyQt5: {e}")
        return False
    
    try:
        import pystray
        print("✅ pystray importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando pystray: {e}")
        return False
    
    try:
        from PIL import Image, ImageDraw
        print("✅ PIL/Pillow importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando PIL: {e}")
        return False
    
    try:
        import keyboard
        print("✅ keyboard importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando keyboard: {e}")
        return False
    
    try:
        import numpy
        print("✅ numpy importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando numpy: {e}")
        return False
    
    return True

def test_modules():
    """Prueba que los módulos locales se importen correctamente."""
    print("\nProbando módulos locales...")
    
    try:
        from config_manager import ConfigManager
        print("✅ ConfigManager importado correctamente")
        
        # Probar funcionalidad básica
        config = ConfigManager("test_config.json")
        config.set_setting("test", "value")
        assert config.get_setting("test") == "value"
        print("✅ ConfigManager funciona correctamente")
        
        # Limpiar archivo de prueba
        if os.path.exists("test_config.json"):
            os.remove("test_config.json")
            
    except Exception as e:
        print(f"❌ Error con ConfigManager: {e}")
        return False
    
    try:
        from filter_effects import FilterEffects
        print("✅ FilterEffects importado correctamente")
        
        # Probar funcionalidad básica
        from PIL import Image
        test_image = Image.new('RGB', (100, 100), (128, 128, 128))
        result = FilterEffects.apply_brightness(test_image, 10)
        assert result is not None
        print("✅ FilterEffects funciona correctamente")
        
    except Exception as e:
        print(f"❌ Error con FilterEffects: {e}")
        return False
    
    return True

def test_qt_app():
    """Prueba que se pueda crear una aplicación Qt básica."""
    print("\nProbando aplicación Qt...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget
        
        app = QApplication(sys.argv)
        widget = QWidget()
        widget.setWindowTitle("Prueba Visual Filter")
        widget.resize(300, 200)
        
        print("✅ Aplicación Qt creada correctamente")
        
        # No mostrar la ventana, solo probar que se puede crear
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ Error creando aplicación Qt: {e}")
        return False

def main():
    """Función principal de prueba."""
    print("=== Prueba de Visual Filter ===")
    print(f"Python: {sys.version}")
    print(f"Sistema: {os.name}")
    print()
    
    # Verificar que estamos en Windows
    if os.name != 'nt':
        print("⚠️  Advertencia: Esta aplicación está diseñada para Windows")
    
    # Ejecutar pruebas
    tests_passed = 0
    total_tests = 3
    
    if test_imports():
        tests_passed += 1
    
    if test_modules():
        tests_passed += 1
    
    if test_qt_app():
        tests_passed += 1
    
    print(f"\n=== Resultados ===")
    print(f"Pruebas pasadas: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 ¡Todas las pruebas pasaron! La aplicación debería funcionar correctamente.")
        print("\nPara ejecutar la aplicación completa:")
        print("python main.py")
        return 0
    else:
        print("❌ Algunas pruebas fallaron. Revisa los errores anteriores.")
        print("\nPara instalar dependencias:")
        print("pip install -r requirements.txt")
        return 1

if __name__ == "__main__":
    sys.exit(main())
